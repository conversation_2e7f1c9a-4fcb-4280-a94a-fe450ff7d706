<UserControl x:Class="DriverManagementSystem.Views.ReportPage1"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Width="794" Height="1123"
             Background="White">

    <Border Background="White" Padding="20">
        <StackPanel>
            <!-- Header Section -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left: Organization Info -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                    <TextBlock Text="الجمهورية اليمنية" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                    <TextBlock Text="فرع دمار والبيضاء" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Center: Title -->
                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="2" Padding="15,10" Margin="20,0">
                    <TextBlock Text="محضر استدراج عروض اسعار" 
                             FontWeight="Bold" FontSize="16" 
                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- Right: Visit Info -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="التاريخ: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding VisitDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="رقم الزيارة: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding VisitNumber}" FontWeight="Bold"/>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <!-- Projects Section -->
            <Border BorderBrush="Black" BorderThickness="1" Margin="0,20">
                <StackPanel>
                    <TextBlock Text="🏗️ القطاع: النقل مقابل العمل" 
                             Background="SteelBlue" Foreground="White" 
                             Padding="10" FontWeight="Bold" FontSize="14"/>

                    <!-- Projects Table -->
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Table Header -->
                        <Border Grid.Row="0" BorderBrush="Black" BorderThickness="1" Background="LightGray">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="عدد الأيام" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Text="اسم المشروع" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="2" Text="رقم المشروع" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                            </Grid>
                        </Border>

                        <!-- Project 1 -->
                        <Border Grid.Row="1" BorderBrush="Black" BorderThickness="1,0,1,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="1" Padding="5" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Text="تنفيذ عدة مكونات لفرعة دمار - جبل زيد - جبل زيد - لحة دمار - دمار" Padding="5" TextWrapping="Wrap"/>
                                <TextBlock Grid.Column="2" Text="911-1397" Padding="5" HorizontalAlignment="Center"/>
                            </Grid>
                        </Border>

                        <!-- Project 2 -->
                        <Border Grid.Row="2" BorderBrush="Black" BorderThickness="1,0,1,1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="2" Padding="5" HorizontalAlignment="Center"/>
                                <TextBlock Grid.Column="1" Text="مشروع عدة تدخلات متنوعة لفرعة الرعية محلات الرعية - بيت ريد - الخضم - الموضع - ابقع - عجلان - بيت القدمة - الكولة - عرية ماور - المنار - دمار" Padding="5" TextWrapping="Wrap"/>
                                <TextBlock Grid.Column="2" Text="911-1396" Padding="5" HorizontalAlignment="Center"/>
                            </Grid>
                        </Border>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Field Visit Data Section -->
            <Border BorderBrush="SteelBlue" BorderThickness="2" Margin="0,20" Background="#F8F9FA">
                <StackPanel Margin="15">
                    <TextBlock Text="🚗 بيانات الزيارة الميدانية" 
                             FontWeight="Bold" FontSize="14" 
                             Foreground="SteelBlue" Margin="0,0,0,15"/>

                    <!-- Activity Type -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="🎯 طبيعة النشاط: " FontWeight="Bold" Width="150"/>
                        <TextBlock Text="التحقق من الدراسات الفنية والتدريع" FontWeight="Normal"/>
                    </StackPanel>

                    <!-- Visit Conductor -->
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="👤 منفذ الزيارة: " FontWeight="Bold" Width="150"/>
                        <TextBlock Text="مهندس علي محمد الشامي" FontWeight="Normal"/>
                    </StackPanel>

                    <!-- Itinerary -->
                    <StackPanel Margin="0,10">
                        <TextBlock Text="📍 خط سير الزيارة والمشاريع المحددة:" FontWeight="Bold" Margin="0,0,0,10"/>
                        <Border BorderBrush="Gray" BorderThickness="1" Padding="10" Background="White">
                            <TextBlock Text="{Binding ItineraryText}" TextWrapping="Wrap" FontSize="12"/>
                        </Border>
                    </StackPanel>

                    <!-- Visit Details -->
                    <StackPanel Margin="0,15">
                        <TextBlock Text="📋 تفاصيل الزيارة الميدانية من المشاريع:" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <!-- Projects Table -->
                        <Border BorderBrush="Black" BorderThickness="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Header -->
                                <Border Grid.Row="0" Background="LightBlue" BorderBrush="Black" BorderThickness="0,0,0,1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="80"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="تسلسل" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="1" Text="تاريخ التنفيذ" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="2" Text="رقم التنفيذ" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="3" Text="اسم المشروع" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="4" Text="الرقم" FontWeight="Bold" Padding="5" HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- Row 1 -->
                                <Border Grid.Row="1" BorderBrush="Black" BorderThickness="0,0,0,1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="80"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="1" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="1" Text="24/06/2025" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="2" Text="3333" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="3" Text="تنفيذ عدة مكونات لفرعة دمار - جبل زيد - جبل زيد - لحة دمار - دمار" Padding="5" TextWrapping="Wrap"/>
                                        <TextBlock Grid.Column="4" Text="911-1397" Padding="5" HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- Row 2 -->
                                <Border Grid.Row="2" BorderBrush="Black" BorderThickness="0,0,0,1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="80"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="2" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="1" Text="24/06/2025" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="2" Text="3333" Padding="5" HorizontalAlignment="Center"/>
                                        <TextBlock Grid.Column="3" Text="مشروع عدة تدخلات متنوعة لفرعة الرعية محلات الرعية - بيت ريد - الخضم - الموضع - ابقع - عجلان - بيت القدمة - الكولة - عرية ماور - المنار - دمار" Padding="5" TextWrapping="Wrap"/>
                                        <TextBlock Grid.Column="4" Text="911-1396" Padding="5" HorizontalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- Footer -->
                                <Border Grid.Row="3" Background="LightGray" Padding="10">
                                    <StackPanel>
                                        <TextBlock Text="ملاحظة: تم تنفيذ الزيارة الميدانية بنجاح وتم التحقق من جميع المشاريع المحددة في خط السير." 
                                                 FontStyle="Italic" TextWrapping="Wrap"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Footer -->
            <Grid Margin="0,30,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="التوقيع" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="مسؤول الحركة" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,20,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="التوقيع" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="مدير الفرع" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,20,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="التوقيع" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="مسؤول الحركة" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,20,0,0"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </Border>
</UserControl>
