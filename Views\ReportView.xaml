<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- A4 Report Container - All Pages in One View -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Edit Contract Button - Hidden during printing -->
        <Border Grid.Row="0" Background="#F8F9FA" Padding="15,10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1"
                x:Name="HeaderButtonsPanel">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="📄 تقرير الزيارة الميدانية"
                         FontSize="16" FontWeight="Bold" Foreground="#495057" VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="🖨️ طباعة مباشرة"
                            Click="PrintReportButton_Click"
                            Background="#28A745"
                            Foreground="White"
                            Padding="20,10"
                            FontWeight="Bold"
                            FontSize="14"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            MinWidth="140">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#218838"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="👁️ معاينة الطباعة"
                            Click="PrintPreviewButton_Click"
                            Background="#17A2B8"
                            Foreground="White"
                            Padding="20,10"
                            FontWeight="Bold"
                            FontSize="14"
                            BorderThickness="0"
                            Margin="0,0,15,0"
                            MinWidth="140">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#138496"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="📝 تحرير العقد"
                            Click="EditContractButton_Click"
                            Background="#6C757D"
                            Foreground="White"
                            Padding="20,10"
                            FontWeight="Bold"
                            FontSize="14"
                            BorderThickness="0"
                            MinWidth="140">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#5A6268"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Report Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" Background="White"
                      x:Name="ReportScrollViewer">
            <StackPanel Background="White" HorizontalAlignment="Center">

                <!-- الصفحة الأولى: محضر استخراج عروض الأسعار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                    <StackPanel Margin="5">

                        <!-- Compact Professional Header -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left Side - Organization Header -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E3A8A" Margin="0,2,0,0"/>
                                <TextBlock Text="فرع ذمار والبيضاء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                            </StackPanel>

                            <!-- Center - Main Title -->
                            <Border Grid.Column="1" Background="#E8F4FD" BorderBrush="#4682B4" BorderThickness="2" Padding="20,10" HorizontalAlignment="Center">
                                <TextBlock Text="محضر استدراج عروض اسعار" FontSize="20" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#1E3A8A" TextAlignment="Center"/>
                            </Border>

                            <!-- Right Side - Date and Visit Number with Icons -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="8,4" Margin="0,0,0,4">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="التاريخ: "/>
                                    <Run Text="{Binding ReportData.ReportDate, FallbackValue='15/06/2025'}"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                                <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="رقم الزيارة: "/>
                                    <Run Text="{Binding ReportData.VisitNumber, FallbackValue='911-13031'}"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>

                        <!-- Compact Projects Section -->
                        <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                            <StackPanel>
                                <!-- Compact Header with Sector -->
                                <Border Background="#F8F9FA" Padding="8,6">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Projects Title on the left -->
                                        <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                            <TextBlock Text="المشاريع التي سيتم زيارتها"
                                                 FontWeight="Bold" FontSize="12" HorizontalAlignment="Left" Foreground="Black"/>
                                        </StackPanel>

                                        <!-- Empty space in center -->
                                        <TextBlock Grid.Column="1"/>

                                        <!-- Sector on the right with Icon -->
                                        <Border Grid.Column="2" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                            <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black">
                                            <Run Text="🏢 القطاع: "/>
                                            <Run Text="{Binding ReportData.SectorName, FallbackValue='الصحة والحماية الاجتماعية'}"/>
                                            </TextBlock>
                                        </Border>
                                    </Grid>
                                </Border>

                                <!-- Enhanced Table Header -->
                                <Border BorderBrush="Black" BorderThickness="2" Background="#E8F4FD">
                                    <Grid MinHeight="35">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="80"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                            <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                            <TextBlock Text="اسم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="2" Padding="8,8">
                                            <TextBlock Text="عدد الأيام" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                    </Grid>
                                </Border>

                                <!-- Enhanced Table Data -->
                                <ItemsControl ItemsSource="{Binding ReportData.Projects, UpdateSourceTrigger=PropertyChanged, NotifyOnTargetUpdated=True}"
                                             x:Name="ProjectsItemsControl"
                                             TargetUpdated="ProjectsItemsControl_TargetUpdated">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                                <Grid MinHeight="35" Background="White">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="120"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="80"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                        <TextBlock Text="{Binding ProjectNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="SemiBold" Foreground="Black"/>
                                                    </Border>
                                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                        <TextBlock Text="{Binding ProjectName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" TextWrapping="Wrap"
                                                             FontSize="11" LineHeight="16" Foreground="Black"/>
                                                    </Border>
                                                    <Border Grid.Column="2" Padding="8,6">
                                                        <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                    </Border>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Compact Visit Data Section -->
                        <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                            <StackPanel>
                                <!-- Compact Header -->
                                <Border Background="#4682B4" Padding="8,6">
                                    <TextBlock Text="بيانات الزيارة الميدانية" FontWeight="Bold" FontSize="11"
                                         Foreground="White" HorizontalAlignment="Center"/>
                                </Border>

                                <!-- Compact Content -->
                                <StackPanel Margin="10,8">
                                    <!-- Activity Nature -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,12">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🎯" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="طبيعة النشاط: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitNature, FallbackValue='اضافة نشاط المهمة'}" Foreground="Black"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>

                                    <!-- Visit Conductor -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,10">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👤" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="القائم بالزيارة: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitConductor, FallbackValue=' '}" Foreground="Black"/>
                                            </TextBlock>
                                        </StackPanel>
                                    </Border>



                                    <!-- Route and Message Frame -->
                                    <Border Margin="0,5,0,5" BorderBrush="Black" BorderThickness="2" Background="White">
                                        <StackPanel>
                                            <!-- Message Header -->
                                            <Border Background="#E8F4FD" Padding="8,6">
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                                    <TextBlock Text="🗺️" FontSize="12" Margin="0,0,8,0" Foreground="Black" VerticalAlignment="Center"/>
                                                    <TextBlock Text="خط السير ونص الرسالة المرسلة للسائقين" FontSize="12" FontWeight="Bold"
                                                         Foreground="Black" VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </Border>

                                            <!-- Message Content -->
                                            <Border Background="White" Padding="10,8">
                                                <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="8" Background="White">
                                                    <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="16" Foreground="Black"
                                                         Text="{Binding ReportData.WinnerDriverMessage, FallbackValue='لم يتم حفظ نص الرسالة للسائق الفائز'}"
                                                         TextAlignment="Left" FontFamily="Segoe UI"/>
                                                </Border>
                                            </Border>
                                        </StackPanel>
                                    </Border>

                                    <!-- Enhanced Dates Section in Single Row -->
                                    <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,8">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <!-- Departure Date -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                                <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="تاريخ النزول:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding ReportData.DepartureDate, FallbackValue='15/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- Return Date -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                                <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="تاريخ العودة:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding ReportData.ReturnDate, FallbackValue='17/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- Days Count -->
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="⏰" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="عدد الأيام:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding ReportData.DaysCount, FallbackValue='3'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                <TextBlock Text=" يوم" FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>

                                    <!-- Simplified Notes Section -->
                                    <StackPanel Orientation="Horizontal" Margin="0,8,0,10">
                                        <TextBlock Text="📝" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Center" MaxWidth="600">
                                        <Run Text="ملاحظات: " FontWeight="Bold" Foreground="Black"/>
                                        <Run Text="{Binding ReportData.Notes, FallbackValue='لا توجد ملاحظات إضافية'}" Foreground="Black"/>
                                        </TextBlock>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Compact Price Offers Table -->
                        <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,0" Background="White">
                            <StackPanel>
                                <!-- Enhanced Header -->
                                <Border Background="#B8D4F0" Padding="10,8">
                                    <TextBlock Text="قائمة الأسعار المقدمة من السائقين" FontWeight="Bold" FontSize="14"
                                         Foreground="Black" HorizontalAlignment="Center"/>
                                </Border>

                                <!-- Enhanced Table Header -->
                                <Border BorderBrush="Black" BorderThickness="2" Background="#F0F8FF">
                                    <Grid MinHeight="35">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="80"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                            <TextBlock Text="الرقم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                            <TextBlock Text="اسم السائق" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                            <TextBlock Text="رقم التلفون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                            <TextBlock Text="السعر المقدم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                        <Border Grid.Column="4" Padding="10">
                                            <TextBlock Text="الحالة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                        </Border>
                                    </Grid>
                                </Border>

                                <!-- Enhanced Table Data -->
                                <ItemsControl ItemsSource="{Binding ReportData.PriceOffers}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                                <Grid MinHeight="35" Background="White">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="60"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="120"/>
                                                        <ColumnDefinition Width="100"/>
                                                        <ColumnDefinition Width="80"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Serial Number -->
                                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                        <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="Black"/>
                                                    </Border>

                                                    <!-- Driver Name -->
                                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                        <TextBlock Text="{Binding DriverName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="SemiBold" Foreground="Black"/>
                                                    </Border>

                                                    <!-- Phone Number -->
                                                    <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                        <TextBlock Text="{Binding PhoneNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="12" FontFamily="Consolas" Foreground="Black"/>
                                                    </Border>

                                                    <!-- Price -->
                                                    <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                            <TextBlock Text="{Binding OfferedPrice, StringFormat='{}{0:N0}'}"
                                                                 FontSize="13" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                            <TextBlock Text=" ريال" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                                        </StackPanel>
                                                    </Border>

                                                    <!-- Status -->
                                                    <Border Grid.Column="4" Padding="10">
                                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                            <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="Bold" VerticalAlignment="Center" Foreground="Black"/>
                                                            <TextBlock Text=" 🏆" FontSize="14" VerticalAlignment="Center" Margin="3,0,0,0">
                                                                <TextBlock.Style>
                                                                    <Style TargetType="TextBlock">
                                                                        <Setter Property="Visibility" Value="Collapsed"/>
                                                                        <Style.Triggers>
                                                                            <DataTrigger Binding="{Binding IsWinner}" Value="True">
                                                                                <Setter Property="Visibility" Value="Visible"/>
                                                                            </DataTrigger>
                                                                        </Style.Triggers>
                                                                    </Style>
                                                                </TextBlock.Style>
                                                            </TextBlock>
                                                        </StackPanel>
                                                    </Border>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </Border>

                        <!-- Simplified Award Decision Section -->
                        <StackPanel Margin="0,0,0,5">
                            <!-- Decision Text and Driver Details in Single Section -->
                            <Border Background="White" Padding="10,5" Margin="0,0,0,0">
                                <StackPanel>
                                    <!-- Decision Text in Single Line with Driver Name - No Wrapping -->
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,5">
                                        <TextBlock Text="بناءً على الأسعار المقدمة من الإخوة أصحاب المركبات الذي تم التواصل معهم أعلاه، فقد تم إرساء النقل على الأخ" FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                        <TextBlock Text=" " FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold" FontSize="14" Foreground="Black" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- Winner Driver Details in Distinguished Frame - Same Line -->
                                    <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="8" Padding="15,10" Margin="0,0,0,0">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <!-- ID -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                <TextBlock Text="🆔" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="رقم البطاقة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding ReportData.WinnerDriver.NationalId}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- Vehicle Type -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                <TextBlock Text="🚙" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="نوع السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleType}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- Capacity -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                                <TextBlock Text="⚖️" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="قدرة السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleCapacity}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <!-- Year -->
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="📅" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="سنة الصنع:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>
                        </StackPanel>


                        <!-- Professional Signatures Section -->
                        <Border Background="White" Padding="15,12" Margin="0,20,0,8">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Task Manager -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="15">
                                        <TextBlock Text="المكلف بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                        <TextBlock HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" TextWrapping="Wrap" MaxWidth="180" Foreground="Black" LineHeight="18"
                                             Text="{Binding ReportData.VisitConductorFormatted, Mode=OneWay, FallbackValue='جمال علي عبدالله الفاطمي &amp; أحمد صالح أحمد حميد'}"/>
                                    </StackPanel>

                                    <!-- Movement Responsible -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="15">
                                        <TextBlock Text="مسئول الحركة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                        <TextBlock Text="علي علي العمدي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                    </StackPanel>

                                    <!-- Branch Manager -->
                                    <StackPanel Grid.Column="2" HorizontalAlignment="Center" Margin="15">
                                        <TextBlock Text="يعتمد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                        <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                        <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="11" FontStyle="Italic" Foreground="Black" Margin="0,4,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- توقيعات الصفحة الأولى -->
                        <StackPanel Margin="0,30,0,20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- توقيع القائم بالزيارة على اليسار -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="10">
                                    <TextBlock Text="القائم بالزيارة" FontWeight="Bold" FontSize="16" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,12"/>
                                    <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Foreground="Black"/>
                                </StackPanel>

                                <!-- توقيع السائق على اليمين -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="10">
                                    <TextBlock Text="السائق" FontWeight="Bold" FontSize="16" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,12"/>
                                    <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Foreground="Black"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- رقم الصفحة الأولى -->
                        <Border Background="Transparent" Padding="0,40,0,20" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                                <TextBlock Text="1" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                            </Border>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- فاصل بين الصفحات -->
                <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                <!-- الصفحة الثانية: عقد إيجار السيارة (الجزء الأول) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                    <StackPanel Margin="20">
                        <!-- رأس العقد المبسط -->
                        <Grid Margin="0,0,0,25">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- شعار المؤسسة بالشمال -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                   Width="100" Height="100"
                                   HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- عنوان العقد بالوسط -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="عقد اتفاق" FontSize="26" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" Margin="0,0,0,5"/>
                                <TextBlock Text="إيجار سيارة" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black"/>
                                <!-- خط تحت العنوان -->
                                <Border Height="2" Background="Black" Width="200" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                            </StackPanel>

                            <!-- رقم العقد ورقم الزيارة باليمين -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                <!-- رقم العقد في إطار -->
                                <Border BorderBrush="#007BFF" BorderThickness="2" Padding="8,4" Margin="0,0,0,5" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- رقم الزيارة في إطار -->
                                <Border BorderBrush="#28A745" BorderThickness="2" Padding="8,4" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding ReportData.VisitNumber, StringFormat='رقم الزيارة: {0}'}" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>

                        <!-- مقدمة العقد مع البيانات الحقيقية -->
                        <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="0,0,0,20">
                            <Run Text="أنه في يوم "/>
                            <Run Text="{Binding ReportData.ContractDayName}" FontWeight="Bold"/>
                            <Run Text=" الموافق "/>
                            <Run Text="{Binding ReportData.ContractDate}" FontWeight="Bold"/>
                            <Run Text=" بمحافظة ذمار، تم إبرام هذا العقد بين كل من:"/>
                        </TextBlock>

                        <!-- أطراف العقد -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- الطرف الأول -->
                            <StackPanel Margin="0,0,0,15">
                                <!-- عنوان الطرف الأول -->
                                <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="300">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                    </Border.Effect>
                                    <TextBlock Text="1- الطرف الأول (مالك السيارة والسائق)" FontSize="16" FontWeight="Bold"
                                         HorizontalAlignment="Left" Foreground="#2C3E50"/>
                                </Border>

                                <!-- بيانات السائق -->
                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                                <Run Text="الأخ/ " FontWeight="Bold"/>
                                <Run Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold"/>
                                <Run Text=" يحمل بطاقة شخصية رقم (" FontWeight="Bold"/>
                                <Run Text="{Binding ReportData.WinnerDriver.NationalId}" FontWeight="Bold"/>
                                <Run Text=") صادرة من: " FontWeight="Bold"/>
                                <Run Text="{Binding ReportData.WinnerDriver.CardIssuePlace}" FontWeight="Bold"/>
                                <Run Text=" بتاريخ: " FontWeight="Bold"/>
                                <Run Text="{Binding ReportData.WinnerDriver.CardIssueDate}" FontWeight="Bold"/>
                                <Run Text="، وبهذا العقد هو مالك السيارة وسائقها الطرف الأول." FontWeight="Bold"/>
                                </TextBlock>
                            </StackPanel>

                            <!-- الطرف الثاني -->
                            <StackPanel Margin="0,0,0,15">
                                <!-- عنوان الطرف الثاني -->
                                <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="350">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                    </Border.Effect>
                                    <TextBlock Text="2- الطرف الثاني (الصندوق الاجتماعي للتنمية)" FontSize="16" FontWeight="Bold"
                                         HorizontalAlignment="Left" Foreground="#2C3E50"/>
                                </Border>

                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0"
                                       Text="{Binding ContractTemplate.SecondPartyTemplate}"/>
                            </StackPanel>
                        </StackPanel>

                        <!-- أولاً: مواصفات السيارة -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان مواصفات السيارة -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="200">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="3- مواصفات السيارة" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                            <Run Text="أجر الطرف الأول للطرف الثاني سيارة "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleType}" FontWeight="Bold"/>
                            <Run Text=" موديل "/>
                            <Run Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontWeight="Bold"/>
                            <Run Text=" لون "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleColor}" FontWeight="Bold"/>
                            <Run Text=" برقم ("/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                            <Run Text=") المملوك له بموجب رخصة تسيير مركبة خصوصي رقم "/>
                            <Run Text="{Binding ReportData.WinnerDriver.LicenseNumber}" FontWeight="Bold"/>
                            <Run Text=" الصادرة بتاريخ "/>
                            <Run Text="{Binding ReportData.WinnerDriver.LicenseIssueDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                            <Run Text=". ويسمى فيما بعد وسيلة النقل بموجب أحكام وشروط هذا العقد."/>
                            </TextBlock>
                        </StackPanel>

                        <!-- ثانياً: غرض الانتفاع -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان غرض الانتفاع -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="180">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="4- غرض الانتفاع" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0"><Run Text="اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/ "/><Run Text=" "/><Run/><Run Text=" "/><Run Text="،  "/><Run Text=" "/><Run/><Run Text=" "/><Run Text=" أثناء تنفيذ المهام الميدانية المطلوبة منه في إطار عمل الصندوق الاجتماعي للتنمية."/></TextBlock>
                        </StackPanel>

                        <!-- ثالثاً: المدة الإيجارية -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان المدة الإيجارية -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="180">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="5- المدة الإيجارية" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                            <Run Text="تاريخ بداية المهمة: "/>
                            <Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold"/>
                            <Run Text=" الموافق "/>
                            <Run Text="{Binding ReportData.DepartureDate}" FontWeight="Bold"/>
                            <Run Text="م، تاريخ انتهاء المهمة: "/>
                            <Run Text="{Binding ReportData.EndDateArabic}" FontWeight="Bold"/>
                            <Run Text=" الموافق "/>
                            <Run Text="{Binding ReportData.ReturnDate}" FontWeight="Bold"/>
                            <Run Text="م، إجمالي عدد الأيام: "/>
                            <Run Text="{Binding ReportData.DaysCount}" FontWeight="Bold"/>
                            <Run Text=" أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد."/>
                            </TextBlock>
                        </StackPanel>

                        <!-- رابعاً: القيمة الإيجارية -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان القيمة الإيجارية -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="250">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="6- القيمة الإيجارية (الأجرة)" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                            <Run Text="اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره ("/>
                            <Run Text="{Binding ReportData.WinnerDriver.WinningPrice, StringFormat='{}{0:N0}'}" FontWeight="Bold"/>
                            <Run Text=") "/>
                            <Run Text="{Binding ReportData.WinnerPriceInArabicText}" FontWeight="Bold"/>
                            <Run Text=" ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة."/>
                            </TextBlock>
                        </StackPanel>

                        <!-- خامساً: إقرار الملكية -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان إقرار الملكية -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="180">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="7- إقرار الملكية" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                            <Run Text="يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير."/>
                            </TextBlock>
                        </StackPanel>

                        <!-- سادساً: التزامات الطرف الأول -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان التزامات الطرف الأول -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="250">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="8- التزامات الطرف الأول" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Margin="10,0,10,0">
                            <Run Text="يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة."/>
                            </TextBlock>

                            <!-- توقيعات تحت النص -->
                            <StackPanel Margin="0,30,0,0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- توقيع السائق على اليمين -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="10">
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,8"/>
                                        <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Foreground="Black"/>
                                    </StackPanel>

                                    <!-- توقيع القائم بالزيارة على اليسار -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="10">
                                        <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,8"/>
                                        <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Foreground="Black"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </StackPanel>

                        <!-- رقم الصفحة الثانية -->
                        <Border Background="Transparent" Padding="0,40,0,20" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                                <TextBlock Text="2" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                            </Border>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- فاصل بين الصفحات -->
                <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                <!-- الصفحة الثالثة: عقد إيجار السيارة (الجزء الثاني) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                    <StackPanel Margin="20">

                        <!-- رأس الصفحة الثالثة -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- شعار المؤسسة بالشمال -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                   Width="100" Height="100"
                                   HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- عنوان العقد بالوسط -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="تابع عقد اتفاق" FontSize="26" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" Margin="0,0,0,5"/>
                                <TextBlock Text="إيجار سيارة" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black"/>
                                <!-- خط تحت العنوان -->
                                <Border Height="2" Background="Black" Width="200" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                            </StackPanel>

                            <!-- رقم العقد ورقم الزيارة باليمين -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                <!-- رقم العقد في إطار -->
                                <Border BorderBrush="#007BFF" BorderThickness="2" Padding="8,4" Margin="0,0,0,5" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- رقم الزيارة في إطار -->
                                <Border BorderBrush="#28A745" BorderThickness="2" Padding="8,4" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding ReportData.VisitNumber, StringFormat='رقم الزيارة: {0}'}" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>

                        <!-- سابعاً: التزامات الطرف الثاني -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان التزامات الطرف الثاني -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="250">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="9- التزامات الطرف الثاني" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="10,0,10,15">
                            <Run Text="يقر الطرف الثاني بأنه عاين كافة الوثائق المطلوبة في وسيلة النقل ووجد أنها مستوفية لكافة لوازمها ولذلك يلتزم بموجب هذا العقد بما يلي:"/>
                            </TextBlock>

                            <!-- Obligation Items -->
                            <StackPanel Margin="30,0,0,0">
                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,8">
                                <Run Text="أ" FontWeight="Bold"/>
                                <Run Text="   التزام ممثليه بتعليمات سائق وسيلة النقل."/>
                                </TextBlock>

                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,8">
                                <Run Text="ب" FontWeight="Bold"/>
                                <Run Text="   التزام ممثليه بوقت وموعد الرحلة."/>
                                </TextBlock>

                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black">
                                <Run Text="ت" FontWeight="Bold"/>
                                <Run Text="   تبليغ السائق على الفور عند إلغاء أو قطع أو تمديد مهمة العمل بدون إنذار مسبق."/>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>

                        <!-- ثامناً: تسوية وحل المنازعات -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان تسوية وحل المنازعات -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="250">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="10- تسوية وحل المنازعات" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="10,0,10,0">
                            <Run Text="كل نزاع ينشأ بين الطرفين أو خلفهما يتعلق بانعقاد أو تنفيذ أو تفسير هذا العقد، أو ما يتفرع عنه أو يرتبط به بأي وجه من الوجوه يتم حله وتسويته بينهما أولاً بالطرق الودية خلال "/>
                            <Run Text="تسعين يوماً" FontWeight="Bold"/>
                            <Run Text="."/>
                            </TextBlock>
                        </StackPanel>

                        <!-- تاسعاً: أحكام أخرى -->
                        <StackPanel Margin="0,0,0,15">
                            <!-- عنوان أحكام أخرى -->
                            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" CornerRadius="5" Padding="10" Margin="0,0,0,5" HorizontalAlignment="Left" Width="180">
                                <Border.Effect>
                                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
                                </Border.Effect>
                                <TextBlock Text="11- أحكام أخرى" FontSize="16" FontWeight="Bold"
                                     HorizontalAlignment="Left" Foreground="#2C3E50"/>
                            </Border>

                            <!-- Provision Items -->
                            <StackPanel Margin="30,0,0,0">
                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,8">
                                <Run Text="⏰" FontWeight="Bold"/>
                                <Run Text="   ينتهي العقد بانتهاء مدته المتفق عليها في العقد دون الحاجة إلى تنبيه أو إنذار ما لم يبلغ الطرف الثاني الطرف الأول برغبته في تمديد المدة."/>
                                </TextBlock>

                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,8">
                                <Run Text="🛡️" FontWeight="Bold"/>
                                <Run Text="   الطرف الأول (مالك السيارة) المسؤول وحده على التأمين على نفسه وعلى سيارته من كافة الحوادث والاعتداءات من الغير، ولا يتحمل الصندوق أي مسؤولية تجاه ما قد يتعرض له أثناء تنفيذ المهمة."/>
                                </TextBlock>

                                <TextBlock FontSize="14" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black">
                                <Run Text="⚠️" FontWeight="Bold"/>
                                <Run Text="   يتحمل الطرف الأول (مالك السيارة والسائق) وحده مسئولية تعويض الراكب عن أي ضرر قد يحدث له نتيجة إصابته بسبب وقوع حادث للسيارة."/>
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>

                        <!-- خاتمة العقد -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="خاتمة العقد" FontWeight="Bold" FontSize="12" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Center" Foreground="Black">
                            <Run Text="حرر هذا العقد من "/>
                            <Run Text="ثلاث نسخ" FontWeight="Bold"/>
                            <Run Text=" بيد كل طرف نسخة منه، والنسخة الثالثة للتوثيق."/>
                            </TextBlock>
                        </StackPanel>

                        <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Margin="0,0,0,25">
                        <Run Text="🤲 ولله الأمر كله وهو على كل شيء شهيد 🤲"/>
                        </TextBlock>

                        <!-- توقيعات أطراف العقد -->
                        <StackPanel Margin="0,0,0,20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- First Party Signature -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="10">
                                    <TextBlock Text="الطرف الأول" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                    <TextBlock Text="(مالك السيارة)" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,20"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold" Foreground="Black" Margin="0,0,0,12"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber}" HorizontalAlignment="Center" FontSize="14" Foreground="#666666"/>
                                </StackPanel>

                                <!-- Second Party Signature -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="10">
                                    <TextBlock Text="الطرف الثاني" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                    <TextBlock Text="القائم بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,20"/>
                                    <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold" Foreground="Black" Margin="0,0,0,12"/>
                                    <TextBlock Text="عبدالله علي ناصر الأصرعي" HorizontalAlignment="Center" FontSize="14" FontWeight="Bold" Foreground="#666666"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- اعتماد مدير الفرع -->
                        <StackPanel Margin="0,30,0,0" HorizontalAlignment="Center">
                            <TextBlock Text="يعتمد مدير الفرع" FontWeight="Bold" FontSize="18" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,20"/>
                            <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="16" FontWeight="Bold" Foreground="Black" Margin="0,0,0,10"/>
                        </StackPanel>

                        <!-- رقم الصفحة الثالثة -->
                        <Border Background="Transparent" Padding="0,40,0,20" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                                <TextBlock Text="3" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                            </Border>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- فاصل بين الصفحات -->
                <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

                <!-- الصفحة الرابعة: بيان وإقرار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                    <StackPanel Margin="20">

                        <!-- رأس الصفحة الرابعة -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- شعار المؤسسة بالشمال -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center">
                                <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                   Width="100" Height="100"
                                   HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- عنوان العقد بالوسط -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="بيان وإقرار" FontSize="26" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black" Margin="0,0,0,5"/>
                                <TextBlock Text="تضارب المصالح والعلاقات العائلية" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"
                                     Foreground="Black"/>
                                <!-- خط تحت العنوان -->
                                <Border Height="2" Background="Black" Width="300" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                            </StackPanel>

                            <!-- رقم العقد ورقم الزيارة باليمين -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center">
                                <!-- رقم العقد في إطار -->
                                <Border BorderBrush="#007BFF" BorderThickness="2" Padding="8,4" Margin="0,0,0,5" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- رقم الزيارة في إطار -->
                                <Border BorderBrush="#28A745" BorderThickness="2" Padding="8,4" CornerRadius="3">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="{Binding ReportData.VisitNumber, StringFormat='رقم الزيارة: {0}'}" FontSize="11" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Grid>

                        <!-- إقرار مزود الخدمة -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="إقرار" FontWeight="Bold" FontSize="12" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black">
                            <Run Text="إقراراً مزود الخدمة صاحب سيارة رقم "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                            <Run Text=" بأنني تسلمت من الصندوق الاجتماعي للتنمية نسخة من "/>
                            <Run Text="مدونة تضارب المصالح" FontWeight="Bold"/>
                            <Run Text="، وخصوص عقدي بلدى العاملين في الصندوق فإنني أقر بما يلي:"/>
                            </TextBlock>
                        </StackPanel>

                        <!-- القسم الأول: العلاقات العائلية -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="القسم الأول: العلاقات العائلية" FontWeight="Bold" FontSize="12"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <TextBlock FontSize="12" Foreground="Black" Margin="0,0,0,10">
                            <Run Text="1) أنه "/>
                            <Run Text="ليس لديه علاقة عائلية" FontWeight="Bold"/>
                            <Run Text=" مع أحد العاملين    ☐ نعم"/>
                            </TextBlock>

                            <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="10" FontWeight="Bold" Foreground="Black" Margin="0,0,0,15"/>

                            <!-- Employee Details Table -->
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Table Headers -->
                                <TextBlock Grid.Column="0" Text="اسم العامل" FontWeight="Bold" FontSize="9" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                                <TextBlock Grid.Column="1" Text="فرع الصندوق" FontWeight="Bold" FontSize="9" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                                <TextBlock Grid.Column="2" Text="نوع العلاقة العائلية" FontWeight="Bold" FontSize="9" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            </Grid>

                            <!-- Empty Rows for Data Entry -->
                            <Grid Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>

                            <Grid Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>
                        </StackPanel>

                        <!-- القسم الثاني: المصالح التجارية -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="القسم الثاني: المصالح التجارية" FontWeight="Bold" FontSize="12"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <TextBlock FontSize="12" Foreground="Black" Margin="0,0,0,10">
                            <Run Text="2) أنه "/>
                            <Run Text="مصالح مباشرة أو غير مباشرة" FontWeight="Bold"/>
                            <Run Text=" مع أحد العاملين    ☐ نعم    ☐ لا"/>
                            </TextBlock>

                            <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="10" FontWeight="Bold" Foreground="Black" Margin="0,0,0,15"/>

                            <!-- Second Table -->
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Table Headers -->
                                <TextBlock Grid.Column="0" Text="اسم العامل" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                                <TextBlock Grid.Column="1" Text="فرع الصندوق" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                                <TextBlock Grid.Column="2" Text="نوع المصلحة" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            </Grid>

                            <!-- Empty Rows -->
                            <Grid Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>

                            <Grid Margin="0,0,0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                                <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            </Grid>
                        </StackPanel>

                        <!-- الإقرار النهائي -->
                        <StackPanel Margin="0,20,0,15">
                            <TextBlock Text="الإقرار النهائي" FontWeight="Bold" FontSize="12" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black">
                            <Run Text="أنا الموقع أدناه أقر بأن جميع البيانات المذكورة أعلاه "/>
                            <Run Text="صحيحة" FontWeight="Bold"/>
                            <Run Text="، وبحق للصندوق اتخاذ الإجراءات التي يراها مناسبة تجاهي في حال عدم صحة البيانات المذكورة."/>
                            </TextBlock>
                        </StackPanel>

                        <TextBlock FontSize="10" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Margin="0,0,0,25">
                        <Run Text="🤲 والله الموفق 🤲"/>
                        </TextBlock>

                        <!-- توقيع -->
                        <StackPanel Margin="0,0,0,0">

                            <StackPanel HorizontalAlignment="Center">
                                <!-- Name Section -->

                                <!-- Signature Section -->
                                <!-- Name Section -->
                                <StackPanel Width="615">
                                    <TextBlock Text="الاسم:" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontSize="12" FontWeight="Bold"
                                         HorizontalAlignment="Center" Foreground="Black"/>
                                </StackPanel>

                                <!-- Signature Section -->
                                <StackPanel>
                                    <TextBlock Text="التوقيع:" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,15"/>
                                    <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>

                        <!-- رقم الصفحة الرابعة -->
                        <Border Background="Transparent" Padding="0,40,0,20" HorizontalAlignment="Center">
                            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                                <TextBlock Text="4" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                            </Border>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- فاصل بين الصفحات -->
                <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>
                <Border Style="{StaticResource PrintPageStyle}">
                    <StackPanel Margin="20">

                        <!-- رأس الصفحة الاحترافي -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>

                            <!-- الشعار والقطاع على اليسار -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="10,0,0,0">
                                <Image Source="C:\Users\<USER>\Desktop\sys\icons\sfd.png"
                                       Width="100" Height="57"
                                       Stretch="Uniform"
                                       />
                            </StackPanel>

                            <!-- العنوان الرئيسي في الوسط مع مربع -->
                            <Border Grid.Column="1"
                                    BorderBrush="Black" BorderThickness="2"
                                    Padding="15,10" Margin="0,-22,0,0"
                                    HorizontalAlignment="Center" VerticalAlignment="Top" Height="50">
                                <TextBlock Text="توثيق الرسائل النصية للنزول الميداني"
                                           FontSize="18" FontWeight="Bold"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Foreground="Black"
                                           TextAlignment="Center"/>
                            </Border>

                            <!-- رقم الزيارة على اليمين -->
                            <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,20,10,0">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <TextBlock Text=": رقم الزيارة" FontSize="14" FontWeight="Normal" Foreground="Black" Width="71"/>
                                    <TextBlock Text="{Binding ReportData.VisitNumber, FallbackValue=911-13026}"
                                               FontSize="14" FontWeight="Bold" Foreground="Black" RenderTransformOrigin="0.746,0.521" Width="73"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                        <!-- خط فاصل تحت القطاع -->
                        <Border Height="2" Background="#333333"/>

                        <!-- محتوى الصفحة - 4 أقسام للصور بالتساوي -->
                        <Grid Height="857" Margin="0,0,0,10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- القسم الأول -->
                            <Border Grid.Row="0" Grid.Column="0" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                <!-- مساحة للصورة الفعلية -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                    <Grid>
                                        <!-- الصورة الفعلية إذا كانت متوفرة -->
                                        <Image x:Name="DocumentationImage1"
                                               Source="{Binding ReportData.DocumentationImage1}"
                                               Stretch="UniformToFill"
                                               Visibility="{Binding ReportData.DocumentationImage1, Converter={StaticResource NullToVisibilityConverter}}"/>

                                        <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage1, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                            <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Border>

                            <!-- القسم الثاني -->
                            <Border Grid.Row="0" Grid.Column="1" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                <!-- مساحة للصورة الفعلية -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                    <Grid>
                                        <!-- الصورة الفعلية إذا كانت متوفرة -->
                                        <Image x:Name="DocumentationImage2"
                                               Source="{Binding ReportData.DocumentationImage2}"
                                               Stretch="UniformToFill"
                                               Visibility="{Binding ReportData.DocumentationImage2, Converter={StaticResource NullToVisibilityConverter}}"/>

                                        <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage2, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                            <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Border>

                            <!-- القسم الثالث -->
                            <Border Grid.Row="1" Grid.Column="0" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                <!-- مساحة للصورة الفعلية -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                    <Grid>
                                        <!-- الصورة الفعلية إذا كانت متوفرة -->
                                        <Image x:Name="DocumentationImage3"
                                               Source="{Binding ReportData.DocumentationImage3}"
                                               Stretch="UniformToFill"
                                               Visibility="{Binding ReportData.DocumentationImage3, Converter={StaticResource NullToVisibilityConverter}}"/>

                                        <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage3, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                            <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Border>

                            <!-- القسم الرابع -->
                            <Border Grid.Row="1" Grid.Column="1" BorderBrush="#2C3E50" BorderThickness="1" Margin="5" Padding="3" CornerRadius="2" Background="White">
                                <!-- مساحة للصورة الفعلية -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="2">
                                    <Grid>
                                        <!-- الصورة الفعلية إذا كانت متوفرة -->
                                        <Image x:Name="DocumentationImage4"
                                               Source="{Binding ReportData.DocumentationImage4}"
                                               Stretch="UniformToFill"
                                               Visibility="{Binding ReportData.DocumentationImage4, Converter={StaticResource NullToVisibilityConverter}}"/>

                                        <!-- أيقونة افتراضية إذا لم تكن الصورة متوفرة -->
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Visibility="{Binding ReportData.DocumentationImage4, ConverterParameter=Inverse, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock Text="📷" FontSize="48" HorizontalAlignment="Center" Foreground="#6C757D"/>
                                            <TextBlock Text="لا توجد صورة" FontSize="12" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,5,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </Border>
                        </Grid>

                        <!-- مسئول الحركة -->
                        <StackPanel HorizontalAlignment="Center" Margin="0,20,0,15"/>
                        <TextBlock Text="مسئول الحركة" FontSize="12" FontWeight="Bold" Foreground="#333333" Width="592"/>

                        <!-- رقم الصفحة الخامسة -->
                        <Border Background="Transparent" Padding="0,10,0,15" HorizontalAlignment="Center">
                            <TextBlock Text="5" FontSize="16" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Left"/>
                        </Border>

                    </StackPanel>
                </Border>

                <!-- الصفحة الخامسة: توثيق الرسائل النصية للنزول الميداني -->

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
