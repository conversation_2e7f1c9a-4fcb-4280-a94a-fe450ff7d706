using System;
using System.Collections.Generic;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Markup;
using DriverManagementSystem.Views;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة مخصصة لصفحة ReportView
    /// </summary>
    public class ReportViewPrintService
    {
        // أبعاد A4 بوحدة DIP
        private const double A4_WIDTH_DIP = 793.7;   // 21.0 cm
        private const double A4_HEIGHT_DIP = 1122.52; // 29.7 cm
        private const double MARGIN_DIP = 37.8;      // 1.0 cm margins

        /// <summary>
        /// طباعة ReportView مباشرة
        /// </summary>
        public static bool PrintReportView(ReportView reportView, string documentTitle = "تقرير الزيارة الميدانية")
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();

                // تحديد إعدادات A4
                try
                {
                    var a4Size = new PageMediaSize(PageMediaSizeName.ISOA4, 210, 297);
                    printDialog.PrintTicket.PageMediaSize = a4Size;
                    printDialog.PrintTicket.PageOrientation = PageOrientation.Portrait;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"تعذر تحديد حجم A4: {ex.Message}");
                }

                // عرض حوار اختيار الطابعة
                if (printDialog.ShowDialog() == true)
                {
                    // استخدام طريقة طباعة مباشرة بدلاً من إنشاء مستند معقد
                    try
                    {
                        var success = PrintReportViewDirectly(reportView, printDialog, documentTitle);
                        if (success)
                        {
                            MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"فشل في الطباعة المباشرة: {ex.Message}");
                    }

                    // جرب طريقة الطباعة باستخدام Bitmap
                    try
                    {
                        var success = PrintReportViewAsBitmap(reportView, printDialog, documentTitle);
                        if (success)
                        {
                            MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"فشل في الطباعة كـ Bitmap: {ex.Message}");
                    }

                    // إذا فشلت الطباعة المباشرة، جرب الطريقة التقليدية
                    var printDocument = CreatePrintDocument(reportView, documentTitle);

                    if (printDocument != null)
                    {
                        // طباعة المستند
                        printDialog.PrintDocument(printDocument.DocumentPaginator, documentTitle);

                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return true;
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء مستند الطباعة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                return false; // المستخدم ألغى الطباعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة من ReportView
        /// </summary>
        private static FixedDocument CreatePrintDocument(ReportView reportView, string title)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء إنشاء مستند الطباعة");

                var fixedDocument = new FixedDocument();

                // إنشاء نسخة للطباعة مع الحفاظ على البيانات
                var printableView = CreatePrintableReportView(reportView);

                if (printableView != null)
                {
                    System.Diagnostics.Debug.WriteLine("🖨️ تم إنشاء النسخة القابلة للطباعة");

                    // تقسيم المحتوى إلى صفحات A4
                    var pages = SplitIntoA4Pages(printableView);

                    System.Diagnostics.Debug.WriteLine($"🖨️ تم تقسيم المحتوى إلى {pages.Count} صفحة");

                    foreach (var pageContent in pages)
                    {
                        var fixedPage = new FixedPage
                        {
                            Width = A4_WIDTH_DIP,
                            Height = A4_HEIGHT_DIP,
                            Background = Brushes.White
                        };

                        // إضافة المحتوى للصفحة
                        fixedPage.Children.Add(pageContent);

                        // إنشاء PageContent
                        var pageContent2 = new PageContent();
                        ((IAddChild)pageContent2).AddChild(fixedPage);

                        fixedDocument.Pages.Add(pageContent2);
                        System.Diagnostics.Debug.WriteLine("🖨️ تم إضافة صفحة للمستند");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في إنشاء النسخة القابلة للطباعة");
                }
                
                System.Diagnostics.Debug.WriteLine($"🖨️ تم إنشاء مستند الطباعة بنجاح مع {fixedDocument.Pages.Count} صفحة");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مستند الطباعة: {ex.Message}");

                // محاولة إنشاء مستند بسيط كبديل
                try
                {
                    return CreateSimplePrintDocument(reportView, title);
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في إنشاء المستند البديل: {ex2.Message}");
                    return null;
                }
            }
        }

        /// <summary>
        /// طباعة ReportView مباشرة بدون نسخ معقد
        /// </summary>
        private static bool PrintReportViewDirectly(ReportView reportView, PrintDialog printDialog, string documentTitle)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء الطباعة المباشرة");

                // إنشاء Visual للطباعة مباشرة من ReportView الأصلي
                var printVisual = CreatePrintVisual(reportView);

                if (printVisual != null)
                {
                    // طباعة Visual مباشرة
                    printDialog.PrintVisual(printVisual, documentTitle);
                    System.Diagnostics.Debug.WriteLine("✅ تم إرسال Visual للطباعة");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في إنشاء Print Visual");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة المباشرة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء Visual للطباعة من ReportView
        /// </summary>
        private static Visual CreatePrintVisual(ReportView reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء Print Visual");

                // إنشاء Grid كحاوي للطباعة
                var printContainer = new Grid
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء نسخة من ReportView مع نفس البيانات
                var reportCopy = new ReportView();
                reportCopy.DataContext = reportView.DataContext;
                reportCopy.Width = A4_WIDTH_DIP - 40;
                reportCopy.Height = A4_HEIGHT_DIP - 40;
                reportCopy.Margin = new Thickness(20);
                reportCopy.Background = Brushes.White;
                reportCopy.FlowDirection = FlowDirection.RightToLeft;

                // إخفاء العناصر التفاعلية للطباعة
                reportCopy.HideInteractiveElementsForPrinting();

                // إجبار تحديث البيانات
                reportCopy.ForceDataRefresh();

                // إضافة ReportView للحاوي
                printContainer.Children.Add(reportCopy);

                // إجبار التخطيط والرسم
                printContainer.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printContainer.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printContainer.UpdateLayout();

                // تحديث إضافي للتأكد من ظهور البيانات
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    reportCopy.UpdateLayout();
                    reportCopy.ForceDataRefresh();
                    printContainer.UpdateLayout();
                }, System.Windows.Threading.DispatcherPriority.Render);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء Print Visual بنجاح");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء Print Visual: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// طباعة ReportView كـ Bitmap
        /// </summary>
        private static bool PrintReportViewAsBitmap(ReportView reportView, PrintDialog printDialog, string documentTitle)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء الطباعة كـ Bitmap");

                // إنشاء نسخة من ReportView للرسم
                var reportCopy = new ReportView();
                reportCopy.DataContext = reportView.DataContext;
                reportCopy.Width = A4_WIDTH_DIP;
                reportCopy.Height = A4_HEIGHT_DIP;
                reportCopy.Background = Brushes.White;
                reportCopy.FlowDirection = FlowDirection.RightToLeft;

                // إخفاء العناصر التفاعلية للطباعة
                reportCopy.HideInteractiveElementsForPrinting();

                // إجبار تحديث البيانات
                reportCopy.ForceDataRefresh();

                // إجبار التخطيط
                reportCopy.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                reportCopy.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                reportCopy.UpdateLayout();

                // تحديث إضافي للتأكد من ظهور البيانات
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    reportCopy.UpdateLayout();
                    reportCopy.ForceDataRefresh();
                }, System.Windows.Threading.DispatcherPriority.Render);

                // إنشاء RenderTargetBitmap
                var renderBitmap = new RenderTargetBitmap(
                    (int)A4_WIDTH_DIP, (int)A4_HEIGHT_DIP, 96, 96, PixelFormats.Pbgra32);

                // رسم ReportView على Bitmap
                renderBitmap.Render(reportCopy);

                // إنشاء Image للطباعة
                var printImage = new Image
                {
                    Source = renderBitmap,
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Stretch = Stretch.None
                };

                // طباعة Image
                printDialog.PrintVisual(printImage, documentTitle);

                System.Diagnostics.Debug.WriteLine("✅ تم إرسال Bitmap للطباعة");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطباعة كـ Bitmap: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة بسيط كبديل
        /// </summary>
        private static FixedDocument CreateSimplePrintDocument(ReportView reportView, string title)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء مستند طباعة بسيط");

                var fixedDocument = new FixedDocument();
                var fixedPage = new FixedPage
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White
                };

                // إنشاء نسخة مباشرة من ReportView
                var reportCopy = new ReportView();
                reportCopy.DataContext = reportView.DataContext;
                reportCopy.Width = A4_WIDTH_DIP - 40;
                reportCopy.Height = A4_HEIGHT_DIP - 40;
                reportCopy.Margin = new Thickness(20);
                reportCopy.Background = Brushes.White;
                reportCopy.FlowDirection = FlowDirection.RightToLeft;

                // إخفاء العناصر التفاعلية للطباعة
                reportCopy.HideInteractiveElementsForPrinting();

                // إجبار تحديث البيانات
                reportCopy.ForceDataRefresh();
                reportCopy.UpdateLayout();

                // إضافة ReportView للصفحة
                fixedPage.Children.Add(reportCopy);

                // إنشاء PageContent
                var pageContent = new PageContent();
                ((IAddChild)pageContent).AddChild(fixedPage);
                fixedDocument.Pages.Add(pageContent);

                System.Diagnostics.Debug.WriteLine("🖨️ تم إنشاء المستند البسيط بنجاح");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند البسيط: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء نسخة قابلة للطباعة من ReportView
        /// </summary>
        private static ReportView CreatePrintableReportView(ReportView originalView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء نسخة قابلة للطباعة من ReportView");

                // إنشاء نسخة جديدة
                var printableView = new ReportView();

                // نسخ DataContext
                printableView.DataContext = originalView.DataContext;
                System.Diagnostics.Debug.WriteLine($"🖨️ تم نسخ DataContext: {originalView.DataContext != null}");

                // تطبيق إعدادات الطباعة
                printableView.Width = A4_WIDTH_DIP;
                printableView.Height = A4_HEIGHT_DIP;
                printableView.Background = Brushes.White;
                printableView.FlowDirection = FlowDirection.RightToLeft;

                // إخفاء العناصر التفاعلية للطباعة
                printableView.HideInteractiveElementsForPrinting();

                // إجبار تحديث البيانات
                printableView.ForceDataRefresh();

                // إجبار التحديث
                printableView.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.UpdateLayout();

                // تحديث إضافي للتأكد من ظهور البيانات
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    printableView.UpdateLayout();
                    printableView.ForceDataRefresh();
                }, System.Windows.Threading.DispatcherPriority.Render);

                System.Diagnostics.Debug.WriteLine("🖨️ تم إنشاء النسخة القابلة للطباعة بنجاح");
                return printableView;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النسخة القابلة للطباعة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تقسيم المحتوى إلى صفحات A4 منفصلة
        /// </summary>
        private static List<FrameworkElement> SplitIntoA4Pages(ReportView reportView)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء تقسيم المحتوى إلى صفحات منفصلة...");

                // البحث عن الصفحات المنفصلة في ReportView
                var separatedPages = ExtractIndividualPages(reportView);

                // إجبار استخدام النماذج المنفصلة مباشرة
                System.Diagnostics.Debug.WriteLine("🔥 إجبار استخدام النماذج المنفصلة...");

                var forcedPages = CreateManualPageSplit(reportView);
                if (forcedPages.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {forcedPages.Count} صفحة بالنماذج المنفصلة");
                    pages.AddRange(forcedPages);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في إنشاء النماذج المنفصلة، استخدام الطريقة القديمة");
                    var fullPage = CreateFullReportPage(reportView);
                    pages.Add(fullPage);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تقسيم الصفحات: {ex.Message}");

                // في حالة الخطأ، أنشئ صفحة واحدة بكل المحتوى
                var fallbackPage = CreateFullReportPage(reportView);
                pages.Add(fallbackPage);
            }

            System.Diagnostics.Debug.WriteLine($"🎯 إجمالي الصفحات: {pages.Count}");
            return pages;
        }

        /// <summary>
        /// الحصول على محتوى التقرير من ReportView (بدون الأزرار)
        /// </summary>
        private static StackPanel GetReportContent(ReportView reportView)
        {
            try
            {
                // البحث عن محتوى التقرير الفعلي بدون الأزرار
                var reportContent = FindReportContentPanel(reportView);
                return reportContent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على محتوى التقرير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// استخراج الصفحات المنفصلة من ReportView
        /// </summary>
        private static List<FrameworkElement> ExtractIndividualPages(ReportView reportView)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                // البحث عن StackPanel الرئيسي الذي يحتوي على الصفحات
                var mainStackPanel = FindMainStackPanel(reportView);
                if (mainStackPanel == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على StackPanel الرئيسي");
                    return pages;
                }

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على StackPanel مع {mainStackPanel.Children.Count} عنصر");

                // البحث عن Border elements التي تحتوي على PrintPageStyle
                int pageCount = 0;
                foreach (UIElement child in mainStackPanel.Children)
                {
                    if (child is Border border)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 فحص Border رقم {pageCount + 1}");

                        if (HasPrintPageStyle(border))
                        {
                            System.Diagnostics.Debug.WriteLine($"📄 تم العثور على صفحة منفصلة رقم {pageCount + 1}");

                            // إنشاء نسخة من الصفحة للطباعة
                            var pageCopy = CreatePageCopy(border);
                            if (pageCopy != null)
                            {
                                pages.Add(pageCopy);
                                pageCount++;
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Border لا يحتوي على PrintPageStyle");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ العنصر ليس Border: {child.GetType().Name}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم استخراج {pages.Count} صفحة منفصلة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استخراج الصفحات: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// البحث عن StackPanel الرئيسي في ReportView
        /// </summary>
        private static StackPanel FindMainStackPanel(ReportView reportView)
        {
            try
            {
                // البحث عن ScrollViewer أولاً
                var scrollViewer = FindChild<ScrollViewer>(reportView);
                if (scrollViewer?.Content is StackPanel stackPanel)
                {
                    return stackPanel;
                }

                // البحث المباشر عن StackPanel
                return FindChild<StackPanel>(reportView);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن StackPanel: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن عنصر فرعي من نوع معين
        /// </summary>
        private static T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }

        /// <summary>
        /// التحقق من أن Border يحتوي على PrintPageStyle
        /// </summary>
        private static bool HasPrintPageStyle(Border border)
        {
            try
            {
                // التحقق من Style
                if (border.Style != null)
                {
                    var styleName = border.Style.ToString();
                    if (styleName.Contains("PrintPageStyle"))
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم العثور على صفحة مع PrintPageStyle");
                        return true;
                    }
                }

                // التحقق من الخصائص المميزة لصفحة الطباعة
                // البحث عن Border كبير يحتوي على محتوى صفحة
                bool isLargeBorder = border.Width > 700 || border.MinHeight > 1000;
                bool hasContent = border.Child != null;

                if (isLargeBorder && hasContent)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة كبيرة: Width={border.Width}, MinHeight={border.MinHeight}");
                    return true;
                }

                // التحقق من المحتوى - إذا كان يحتوي على StackPanel مع محتوى كثير
                if (border.Child is StackPanel stackPanel && stackPanel.Children.Count > 5)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة مع محتوى كثير: {stackPanel.Children.Count} عنصر");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من PrintPageStyle: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة من الصفحة للطباعة
        /// </summary>
        private static FrameworkElement CreatePageCopy(Border originalPage)
        {
            try
            {
                // إنشاء حاوي جديد للصفحة
                var pageContainer = new Border
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    BorderBrush = Brushes.Transparent,
                    BorderThickness = new Thickness(0),
                    Margin = new Thickness(0)
                };

                // نسخ المحتوى
                if (originalPage.Child is FrameworkElement content)
                {
                    var contentCopy = CloneElement(content);
                    pageContainer.Child = contentCopy;
                }

                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء نسخة الصفحة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تقسيم يدوي للصفحات كطريقة بديلة - استخدام النماذج المنفصلة
        /// </summary>
        private static List<FrameworkElement> CreateManualPageSplit(ReportView reportView)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء التقسيم اليدوي للصفحات باستخدام النماذج المنفصلة...");

                // إنشاء الصفحة الأولى (محضر استدراج عروض الأسعار)
                var page1 = CreateSeparateReportPage(reportView);
                if (page1 != null)
                {
                    pages.Add(page1);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الصفحة الأولى - محضر استدراج عروض الأسعار");
                }

                // إنشاء الصفحة الثانية (عقد إيجار السيارة - الجزء الأول)
                var page2 = CreateSeparateContractPage1(reportView);
                if (page2 != null)
                {
                    pages.Add(page2);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الصفحة الثانية - عقد إيجار السيارة (الجزء الأول)");
                }

                // إنشاء الصفحة الثالثة (عقد إيجار السيارة - الجزء الثاني)
                var page3 = CreateSeparateContractPage2(reportView);
                if (page3 != null)
                {
                    pages.Add(page3);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الصفحة الثالثة - عقد إيجار السيارة (الجزء الثاني)");
                }

                System.Diagnostics.Debug.WriteLine($"🎯 تم إنشاء {pages.Count} صفحة منفصلة بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التقسيم اليدوي: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// إنشاء صفحة التقرير المنفصلة (محضر استدراج عروض الأسعار)
        /// </summary>
        private static FrameworkElement CreateSeparateReportPage(ReportView reportView)
        {
            try
            {
                var reportPage = new Views.ReportPage1();
                reportPage.DataContext = reportView.DataContext;
                reportPage.Width = A4_WIDTH_DIP;
                reportPage.Height = A4_HEIGHT_DIP;
                reportPage.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة التقرير المنفصلة");
                return reportPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة التقرير المنفصلة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة العقد الأولى المنفصلة
        /// </summary>
        private static FrameworkElement CreateSeparateContractPage1(ReportView reportView)
        {
            try
            {
                var contractPage1 = new Views.ContractPage1();
                contractPage1.DataContext = reportView.DataContext;
                contractPage1.Width = A4_WIDTH_DIP;
                contractPage1.Height = A4_HEIGHT_DIP;
                contractPage1.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة العقد الأولى المنفصلة");
                return contractPage1;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة العقد الأولى المنفصلة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة العقد الثانية المنفصلة
        /// </summary>
        private static FrameworkElement CreateSeparateContractPage2(ReportView reportView)
        {
            try
            {
                var contractPage2 = new Views.ContractPage2();
                contractPage2.DataContext = reportView.DataContext;
                contractPage2.Width = A4_WIDTH_DIP;
                contractPage2.Height = A4_HEIGHT_DIP;
                contractPage2.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة العقد الثانية المنفصلة");
                return contractPage2;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة العقد الثانية المنفصلة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق
        /// </summary>
        private static FrameworkElement CloneElement(FrameworkElement original)
        {
            try
            {
                // استخدام XamlWriter و XamlReader لنسخ العنصر
                var xaml = XamlWriter.Save(original);
                var cloned = (FrameworkElement)XamlReader.Parse(xaml);

                // نسخ DataContext إذا كان موجوداً
                if (original.DataContext != null)
                {
                    cloned.DataContext = original.DataContext;
                }

                return cloned;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر: {ex.Message}");
                return original; // إرجاع العنصر الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// البحث عن StackPanel الذي يحتوي على محتوى التقرير فقط
        /// </summary>
        private static StackPanel FindReportContentPanel(DependencyObject parent)
        {
            try
            {
                if (parent == null) return null;

                // البحث عن ScrollViewer في Grid.Row="1"
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);

                    // إذا كان ScrollViewer
                    if (child is ScrollViewer scrollViewer)
                    {
                        // التحقق من أنه في الصف الثاني (محتوى التقرير)
                        var rowValue = Grid.GetRow(scrollViewer);
                        if (rowValue == 1 && scrollViewer.Content is StackPanel contentStack)
                        {
                            return contentStack;
                        }
                    }

                    // البحث المتكرر في العناصر الفرعية
                    var result = FindReportContentPanel(child);
                    if (result != null)
                        return result;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن محتوى التقرير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تقسيم المحتوى إلى صفحات منفصلة
        /// </summary>
        private static List<FrameworkElement> SeparateContentIntoPages(StackPanel originalContent)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                // تكرار عبر العناصر الفرعية في StackPanel
                foreach (UIElement child in originalContent.Children)
                {
                    if (child is Border border)
                    {
                        // التحقق من أن Border يحتوي على محتوى صفحة (وليس أزرار)
                        if (IsReportPageBorder(border))
                        {
                            // إنشاء صفحة منفصلة لكل Border (صفحة)
                            var separatePage = CreateSeparatePage(border);
                            if (separatePage != null)
                                pages.Add(separatePage);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تقسيم المحتوى: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// التحقق من أن Border يحتوي على محتوى صفحة تقرير
        /// </summary>
        private static bool IsReportPageBorder(Border border)
        {
            try
            {
                // التحقق من Style أو خصائص Border
                if (border.Style != null)
                {
                    // إذا كان له Style يسمى "PrintPageStyle" فهو صفحة تقرير
                    return true;
                }

                // التحقق من المحتوى الداخلي
                if (border.Child is StackPanel stackPanel)
                {
                    // البحث عن عناصر تدل على أنها صفحة تقرير (مثل جداول أو نصوص)
                    foreach (UIElement child in stackPanel.Children)
                    {
                        if (child is Grid || child is TextBlock || child is Border)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch
            {
                return true; // في حالة الشك، اعتبرها صفحة تقرير
            }
        }

        /// <summary>
        /// إنشاء صفحة منفصلة من Border
        /// </summary>
        private static FrameworkElement CreateSeparatePage(Border originalBorder)
        {
            try
            {
                // إنشاء صفحة A4 كاملة
                var pageContainer = new Border
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    Padding = new Thickness(20) // هوامش صغيرة للطباعة
                };

                // نسخ محتوى Border الأصلي
                var contentCopy = CloneBorderContent(originalBorder);

                if (contentCopy != null)
                {
                    // تطبيق تحسينات الدقة
                    ApplyHighQualityRendering(contentCopy);

                    // تعيين حجم المحتوى ليملأ الصفحة
                    contentCopy.Width = A4_WIDTH_DIP - 40; // مع مراعاة الهوامش
                    contentCopy.Height = A4_HEIGHT_DIP - 40;
                    contentCopy.HorizontalAlignment = HorizontalAlignment.Stretch;
                    contentCopy.VerticalAlignment = VerticalAlignment.Stretch;

                    pageContainer.Child = contentCopy;
                }

                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحة منفصلة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة المحضر
        /// </summary>
        private static FrameworkElement CreateReportPage(ReportView reportView)
        {
            try
            {
                var pageContainer = new Border
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    Padding = new Thickness(MARGIN_DIP)
                };

                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء صفحة المحضر مع البيانات");

                // إنشاء نسخة من ReportView للصفحة الأولى فقط
                var reportCopy = new ReportView();
                reportCopy.DataContext = reportView.DataContext;

                System.Diagnostics.Debug.WriteLine($"🖨️ DataContext للنسخة: {reportCopy.DataContext != null}");

                // إخفاء العناصر التفاعلية للطباعة
                reportCopy.HideInteractiveElementsForPrinting();

                // تحسين الدقة
                RenderOptions.SetBitmapScalingMode(reportCopy, BitmapScalingMode.HighQuality);
                RenderOptions.SetEdgeMode(reportCopy, EdgeMode.Aliased);
                TextOptions.SetTextFormattingMode(reportCopy, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(reportCopy, TextRenderingMode.ClearType);

                // إجبار تحديث البيانات قبل الطباعة
                reportCopy.ForceDataRefresh();
                reportCopy.UpdateLayout();

                // تحديث إضافي للتأكد من ظهور البيانات
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    reportCopy.UpdateLayout();
                    reportCopy.ForceDataRefresh();
                }, System.Windows.Threading.DispatcherPriority.Render);

                var viewbox = new Viewbox
                {
                    Stretch = Stretch.Uniform,
                    StretchDirection = StretchDirection.DownOnly,
                    Child = reportCopy
                };

                pageContainer.Child = viewbox;
                System.Diagnostics.Debug.WriteLine("🖨️ تم إنشاء صفحة المحضر بنجاح");
                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحة المحضر: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحات العقد
        /// </summary>
        private static List<FrameworkElement> CreateContractPages(ReportView reportView)
        {
            var contractPages = new List<FrameworkElement>();

            try
            {
                // يمكن إضافة منطق لإنشاء صفحات العقد منفصلة هنا
                // حالياً سنتركها فارغة ونركز على صفحة المحضر
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحات العقد: {ex.Message}");
            }

            return contractPages;
        }

        /// <summary>
        /// نسخ محتوى Border
        /// </summary>
        private static FrameworkElement CloneBorderContent(Border originalBorder)
        {
            try
            {
                // إنشاء نسخة من Border مع تحسينات الحجم
                var newBorder = new Border
                {
                    Background = originalBorder.Background ?? Brushes.White,
                    BorderBrush = originalBorder.BorderBrush,
                    BorderThickness = originalBorder.BorderThickness,
                    Padding = new Thickness(10), // هوامش داخلية صغيرة
                    Margin = new Thickness(0),
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch
                };

                // نسخ المحتوى الداخلي
                if (originalBorder.Child != null)
                {
                    var contentCopy = CloneUIElement(originalBorder.Child);

                    // تحسين حجم المحتوى المنسوخ
                    if (contentCopy is FrameworkElement frameworkElement)
                    {
                        frameworkElement.HorizontalAlignment = HorizontalAlignment.Stretch;
                        frameworkElement.VerticalAlignment = VerticalAlignment.Stretch;

                        // إزالة قيود الحجم الثابت
                        frameworkElement.Width = double.NaN;
                        frameworkElement.Height = double.NaN;
                        frameworkElement.MaxWidth = double.PositiveInfinity;
                        frameworkElement.MaxHeight = double.PositiveInfinity;
                    }

                    newBorder.Child = contentCopy;
                }

                return newBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ محتوى Border: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات
        /// </summary>
        private static UIElement CloneUIElement(UIElement original)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖨️ نسخ عنصر UI: {original.GetType().Name}");

                // إذا كان العنصر ReportView، إنشاء نسخة جديدة مع نفس DataContext
                if (original is ReportView originalReportView)
                {
                    var newReportView = new ReportView();
                    newReportView.DataContext = originalReportView.DataContext;
                    newReportView.Width = originalReportView.Width;
                    newReportView.Height = originalReportView.Height;
                    newReportView.Background = originalReportView.Background;
                    newReportView.FlowDirection = originalReportView.FlowDirection;

                    // إجبار تحديث البيانات
                    newReportView.ForceDataRefresh();
                    newReportView.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine("🖨️ تم إنشاء نسخة جديدة من ReportView مع البيانات");
                    return newReportView;
                }

                // للعناصر الأخرى، استخدام الطريقة التقليدية
                var xaml = XamlWriter.Save(original);
                var cloned = (UIElement)XamlReader.Parse(xaml);

                // إذا كان العنصر المنسوخ يحتوي على DataContext، نسخه
                if (original is FrameworkElement originalFE && cloned is FrameworkElement clonedFE)
                {
                    if (originalFE.DataContext != null)
                    {
                        clonedFE.DataContext = originalFE.DataContext;
                        System.Diagnostics.Debug.WriteLine("🖨️ تم نسخ DataContext للعنصر");
                    }
                }

                return cloned;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر: {ex.Message}");
                // في حالة فشل النسخ، إرجاع العنصر الأصلي
                return original;
            }
        }

        /// <summary>
        /// تطبيق تحسينات الدقة العالية
        /// </summary>
        private static void ApplyHighQualityRendering(FrameworkElement element)
        {
            try
            {
                RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
                RenderOptions.SetEdgeMode(element, EdgeMode.Aliased);
                TextOptions.SetTextFormattingMode(element, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(element, TextRenderingMode.ClearType);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق تحسينات الدقة: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صفحة كاملة بكل المحتوى (احتياطي)
        /// </summary>
        private static FrameworkElement CreateFullReportPage(ReportView reportView)
        {
            var pageContainer = new Border
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White,
                Padding = new Thickness(20)
            };

            System.Diagnostics.Debug.WriteLine("🖨️ إنشاء صفحة كاملة من ReportView");

            // إنشاء نسخة كاملة من ReportView
            var reportCopy = new ReportView();
            reportCopy.DataContext = reportView.DataContext;

            System.Diagnostics.Debug.WriteLine($"🖨️ DataContext للصفحة الكاملة: {reportCopy.DataContext != null}");

            // إخفاء العناصر التفاعلية للطباعة
            reportCopy.HideInteractiveElementsForPrinting();

            // تعيين حجم ReportView ليملأ الصفحة
            reportCopy.Width = A4_WIDTH_DIP - 40;
            reportCopy.Height = A4_HEIGHT_DIP - 40;
            reportCopy.HorizontalAlignment = HorizontalAlignment.Stretch;
            reportCopy.VerticalAlignment = VerticalAlignment.Stretch;

            // إجبار تحديث البيانات قبل تطبيق التحسينات
            reportCopy.ForceDataRefresh();
            reportCopy.UpdateLayout();

            // تطبيق تحسينات الدقة
            ApplyHighQualityRendering(reportCopy);

            // تحديث إضافي للتأكد من ظهور البيانات
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                reportCopy.UpdateLayout();
                reportCopy.ForceDataRefresh();
            }, System.Windows.Threading.DispatcherPriority.Render);

            // استخدام ScrollViewer للمحتوى الطويل
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Hidden,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden,
                Content = reportCopy,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            pageContainer.Child = scrollViewer;
            return pageContainer;
        }

        /// <summary>
        /// إنشاء صفحة خطأ
        /// </summary>
        private static FrameworkElement CreateErrorPage(string errorMessage)
        {
            var errorBorder = new Border
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White,
                BorderBrush = Brushes.Red,
                BorderThickness = new Thickness(2),
                Padding = new Thickness(50)
            };

            var errorText = new TextBlock
            {
                Text = $"خطأ في الطباعة:\n\n{errorMessage}",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.Red,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                TextAlignment = TextAlignment.Center
            };

            errorBorder.Child = errorText;
            return errorBorder;
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        public static void ShowPrintPreview(ReportView reportView)
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للمعاينة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إنشاء مستند للمعاينة
                var printDocument = CreatePrintDocument(reportView, "معاينة التقرير");
                
                if (printDocument != null)
                {
                    // فتح نافذة المعاينة المتطورة
                    var previewWindow = new ProfessionalPrintPreviewWindow();
                    previewWindow.SetPrintDocument(printDocument);
                    previewWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("فشل في إنشاء معاينة الطباعة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
