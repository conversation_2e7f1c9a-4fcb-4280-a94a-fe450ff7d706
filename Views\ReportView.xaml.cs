using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            DataContext = new ReportViewModel();

            // إضافة معالج لتحديث البيانات عند تغيير DataContext
            this.DataContextChanged += ReportView_DataContextChanged;
        }

        private void ReportView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 DataContext تغير في ReportView");

                // إجبار تحديث ItemsControl للمشاريع
                if (ProjectsItemsControl != null)
                {
                    ProjectsItemsControl.UpdateLayout();
                    ProjectsItemsControl.InvalidateVisual();
                    System.Diagnostics.Debug.WriteLine("🔄 تم تحديث ItemsControl للمشاريع");
                }

                // إجبار تحديث التخطيط العام
                this.UpdateLayout();
                this.InvalidateVisual();

                // تحديث إضافي بعد تأخير قصير
                this.Dispatcher.BeginInvoke(new System.Action(() =>
                {
                    ForceDataRefresh();
                }), System.Windows.Threading.DispatcherPriority.Background);

                System.Diagnostics.Debug.WriteLine("🔄 تم تحديث ReportView بالكامل");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.ShowPrintPreview(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إجبار تحديث البيانات في التقرير
        /// </summary>
        public void ForceDataRefresh()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إجبار تحديث البيانات في ReportView");

                // إجبار تحديث ItemsControl للمشاريع
                if (ProjectsItemsControl != null)
                {
                    // تحديث مصدر البيانات
                    var binding = ProjectsItemsControl.GetBindingExpression(ItemsControl.ItemsSourceProperty);
                    if (binding != null)
                    {
                        binding.UpdateTarget();
                        System.Diagnostics.Debug.WriteLine("🔄 تم تحديث binding للمشاريع");
                    }

                    ProjectsItemsControl.Items.Refresh();
                    ProjectsItemsControl.UpdateLayout();
                    ProjectsItemsControl.InvalidateVisual();
                    System.Diagnostics.Debug.WriteLine("🔄 تم إجبار تحديث بيانات المشاريع");
                }

                // إجبار تحديث التخطيط العام
                this.UpdateLayout();
                this.InvalidateVisual();

                // تحديث جميع bindings في الصفحة
                var bindings = this.GetBindingExpression(DataContextProperty);
                if (bindings != null)
                {
                    bindings.UpdateTarget();
                    System.Diagnostics.Debug.WriteLine("🔄 تم تحديث DataContext binding");
                }

                System.Diagnostics.Debug.WriteLine("🔄 تم إجبار تحديث التخطيط العام للتقرير");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إجبار تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تحديث البيانات للمشاريع
        /// </summary>
        private void ProjectsItemsControl_TargetUpdated(object sender, System.Windows.Data.DataTransferEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 ProjectsItemsControl_TargetUpdated triggered");

                if (ProjectsItemsControl != null)
                {
                    ProjectsItemsControl.UpdateLayout();
                    System.Diagnostics.Debug.WriteLine($"🔄 عدد العناصر في ItemsControl: {ProjectsItemsControl.Items.Count}");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالج تحديث المشاريع: {ex.Message}");
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
