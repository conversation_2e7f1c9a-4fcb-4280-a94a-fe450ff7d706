using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            DataContext = new ReportViewModel();

            // إضافة معالج لتحديث البيانات عند تغيير DataContext
            this.DataContextChanged += ReportView_DataContextChanged;
        }

        private void ReportView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            try
            {
                // إجبار تحديث ItemsControl للمشاريع
                if (ProjectsItemsControl != null)
                {
                    ProjectsItemsControl.UpdateLayout();
                    System.Diagnostics.Debug.WriteLine("🔄 تم تحديث ItemsControl للمشاريع");
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.ShowPrintPreview(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إجبار تحديث البيانات في التقرير
        /// </summary>
        public void ForceDataRefresh()
        {
            try
            {
                // إجبار تحديث ItemsControl للمشاريع
                if (ProjectsItemsControl != null)
                {
                    ProjectsItemsControl.Items.Refresh();
                    ProjectsItemsControl.UpdateLayout();
                    System.Diagnostics.Debug.WriteLine("🔄 تم إجبار تحديث بيانات المشاريع");
                }

                // إجبار تحديث التخطيط العام
                this.UpdateLayout();
                System.Diagnostics.Debug.WriteLine("🔄 تم إجبار تحديث التخطيط العام للتقرير");
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إجبار تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
