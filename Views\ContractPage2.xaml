<UserControl x:Class="DriverManagementSystem.Views.ContractPage2"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Width="794" Height="1123"
             Background="White">

    <Border Background="White" Padding="20">
        <StackPanel>
            <!-- Header Section -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left: Logo -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                    <Border Width="80" Height="80" Background="LightGray" 
                            BorderBrush="Black" BorderThickness="1">
                        <TextBlock Text="شعار" HorizontalAlignment="Center" 
                                 VerticalAlignment="Center" FontWeight="Bold"/>
                    </Border>
                </StackPanel>

                <!-- Center: Title -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="عقد اتفاق ايجار سيارة" 
                             FontWeight="Bold" FontSize="20" 
                             HorizontalAlignment="Center" Margin="0,10"/>
                    <TextBlock Text="(تتمة)" 
                             FontWeight="Bold" FontSize="16" 
                             HorizontalAlignment="Center" Margin="0,5"/>
                </StackPanel>

                <!-- Right: Contract Info -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="رقم العقد: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding VisitNumber}" FontWeight="Bold"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="الصفحة: 2" FontWeight="Bold"/>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <!-- Contract Terms Continuation -->
            <StackPanel Margin="0,20">
                <!-- Term 6 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند السادس: إنهاء العقد" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock Text="• يحق لأي من الطرفين إنهاء هذا العقد بإشعار مسبق 24 ساعة." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• في حالة الإخلال بأي من بنود العقد من قبل أي طرف." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• في حالة القوة القاهرة التي تمنع تنفيذ العقد." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>

                <!-- Term 7 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند السابع: المسؤولية والتأمين" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock Text="• الطرف الثاني مسؤول عن تأمين السيارة ضد الحوادث." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• الطرف الثاني يتحمل مسؤولية أي أضرار تلحق بالسيارة." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• الطرف الأول غير مسؤول عن أي حوادث قد تحدث أثناء النقل." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>

                <!-- Term 8 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الثامن: فض النزاعات" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <TextBlock Text="في حالة نشوء أي نزاع حول تفسير أو تنفيذ هذا العقد، يتم حله بالتفاوض المباشر بين الطرفين، وفي حالة عدم التوصل لحل، يتم اللجوء للقضاء المختص."
                             TextWrapping="Wrap" FontSize="11" Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Term 9 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند التاسع: أحكام عامة" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock Text="• هذا العقد يلغي أي اتفاقيات سابقة بين الطرفين حول نفس الموضوع." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• لا يجوز تعديل هذا العقد إلا بموافقة كتابية من الطرفين." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• يخضع هذا العقد للقوانين اليمنية النافذة." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>

                <!-- Vehicle Details -->
                <Border BorderBrush="Black" BorderThickness="2" Margin="0,30" Padding="15">
                    <StackPanel>
                        <TextBlock Text="بيانات السيارة المستخدمة:" FontWeight="Bold" FontSize="14" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="نوع السيارة: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.VehicleType}" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="رقم اللوحة: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.LicensePlate}" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="سنة الصنع: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.VehicleYear}" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="لون السيارة: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.VehicleColor}" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="رقم الشاصي: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.ChassisNumber}" FontSize="12"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="رقم المحرك: " FontWeight="Bold" Width="100"/>
                                    <TextBlock Text="{Binding SelectedDriver.EngineNumber}" FontSize="12"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Final Terms -->
                <StackPanel Margin="0,20">
                    <TextBlock Text="البند العاشر: سريان العقد" FontWeight="Bold" FontSize="12" Margin="0,0,0,10"/>
                    <TextBlock Text="يسري مفعول هذا العقد من تاريخ توقيعه من قبل الطرفين، وقد تم تحرير هذا العقد من نسختين أصليتين، لكل طرف نسخة للعمل بموجبها."
                             TextWrapping="Wrap" FontSize="11" Margin="10,0,0,0"/>
                </StackPanel>
            </StackPanel>

            <!-- Final Signatures -->
            <Grid Margin="0,50,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="الطرف الأول" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5"/>
                    <TextBlock Text="فرع دمار والبيضاء" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,2"/>
                    <Border BorderBrush="Black" BorderThickness="0,0,0,1" Width="150" Margin="0,30,0,5">
                        <TextBlock Text="" Height="2"/>
                    </Border>
                    <TextBlock Text="التوقيع والختم" HorizontalAlignment="Center" FontSize="10"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="الطرف الثاني" FontWeight="Bold" HorizontalAlignment="Center" FontSize="14"/>
                    <TextBlock Text="{Binding SelectedDriver.Name}" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5"/>
                    <TextBlock Text="مالك السيارة والسائق" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,2"/>
                    <Border BorderBrush="Black" BorderThickness="0,0,0,1" Width="150" Margin="0,30,0,5">
                        <TextBlock Text="" Height="2"/>
                    </Border>
                    <TextBlock Text="التوقيع" HorizontalAlignment="Center" FontSize="10"/>
                </StackPanel>
            </Grid>

            <!-- Date and Place -->
            <StackPanel HorizontalAlignment="Center" Margin="0,30,0,0">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="حُرر في: دمار، بتاريخ " FontWeight="Bold"/>
                    <TextBlock Text="{Binding VisitDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                </StackPanel>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl>
