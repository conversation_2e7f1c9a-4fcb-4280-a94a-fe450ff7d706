<UserControl x:Class="DriverManagementSystem.Views.ContractPage1"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Width="794" Height="1123"
             Background="White">

    <Border Background="White" Padding="20">
        <StackPanel>
            <!-- Header Section -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left: Logo -->
                <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                    <Border Width="80" Height="80" Background="LightGray" 
                            BorderBrush="Black" BorderThickness="1">
                        <TextBlock Text="شعار" HorizontalAlignment="Center" 
                                 VerticalAlignment="Center" FontWeight="Bold"/>
                    </Border>
                </StackPanel>

                <!-- Center: Title -->
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="عقد اتفاق ايجار سيارة" 
                             FontWeight="Bold" FontSize="20" 
                             HorizontalAlignment="Center" Margin="0,10"/>
                    <Border BorderBrush="Black" BorderThickness="2" Padding="10,5">
                        <TextBlock Text="Contract Agreement for Vehicle Rental" 
                                 FontWeight="Bold" FontSize="14" 
                                 HorizontalAlignment="Center"/>
                    </Border>
                </StackPanel>

                <!-- Right: Contract Info -->
                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="رقم العقد: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding VisitNumber}" FontWeight="Bold"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,5">
                        <TextBlock Text="رقم الزيارة: " FontWeight="Bold"/>
                        <TextBlock Text="{Binding VisitNumber}" FontWeight="Bold"/>
                    </StackPanel>
                </StackPanel>
            </Grid>

            <!-- Contract Content -->
            <StackPanel Margin="0,20">
                <!-- Party 1 -->
                <TextBlock Text="الطرف الأول:" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                <TextBlock Text="الصندوق الاجتماعي للتنمية - فرع دمار والبيضاء، ويمثله في هذا العقد مدير الفرع أو من ينوب عنه، ويشار إليه فيما بعد بـ (الطرف الأول)."
                         TextWrapping="Wrap" Margin="20,0,0,20" FontSize="12"/>

                <!-- Party 2 -->
                <TextBlock Text="الطرف الثاني:" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                <StackPanel Margin="20,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="الاسم: " FontWeight="Bold" Width="80"/>
                        <TextBlock Text="{Binding SelectedDriver.Name}" FontSize="12"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="رقم الهوية: " FontWeight="Bold" Width="80"/>
                        <TextBlock Text="{Binding SelectedDriver.IdNumber}" FontSize="12"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="رقم الهاتف: " FontWeight="Bold" Width="80"/>
                        <TextBlock Text="{Binding SelectedDriver.Phone}" FontSize="12"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="العنوان: " FontWeight="Bold" Width="80"/>
                        <TextBlock Text="{Binding SelectedDriver.Address}" FontSize="12"/>
                    </StackPanel>
                    <TextBlock Text="ويشار إليه فيما بعد بـ (الطرف الثاني)." FontSize="12" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Contract Terms -->
                <TextBlock Text="بنود العقد:" FontWeight="Bold" FontSize="14" Margin="0,20,0,10"/>
                
                <!-- Term 1 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الأول: موضوع العقد" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <TextBlock Text="يتعهد الطرف الثاني بتوفير خدمات النقل للطرف الأول باستخدام سيارته الخاصة لتنفيذ الزيارات الميدانية للمشاريع التنموية التابعة للصندوق الاجتماعي للتنمية."
                             TextWrapping="Wrap" FontSize="11" Margin="10,0,0,0"/>
                </StackPanel>

                <!-- Term 2 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الثاني: مدة العقد" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal" Margin="10,0,0,0">
                        <TextBlock Text="مدة هذا العقد " FontSize="11"/>
                        <TextBlock Text="{Binding TotalDays}" FontWeight="Bold" FontSize="11"/>
                        <TextBlock Text=" أيام، تبدأ من تاريخ " FontSize="11"/>
                        <TextBlock Text="{Binding VisitDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" FontSize="11"/>
                        <TextBlock Text=" وتنتهي بانتهاء المهمة المحددة." FontSize="11"/>
                    </StackPanel>
                </StackPanel>

                <!-- Term 3 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الثالث: التزامات الطرف الثاني" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock Text="• توفير سيارة صالحة للسير ومرخصة حسب الأصول القانونية." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• الالتزام بمواعيد العمل المحددة من قبل الطرف الأول." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• توفير السائق المؤهل وحامل رخصة قيادة سارية المفعول." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• الالتزام بقواعد السلامة والأمان أثناء النقل." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• عدم استخدام السيارة لأغراض أخرى خلال فترة العقد." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>

                <!-- Term 4 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الرابع: التزامات الطرف الأول" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <TextBlock Text="• دفع أجرة النقل المتفق عليها في المواعيد المحددة." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• توفير كافة المعلومات اللازمة عن خط السير والمواقع." FontSize="11" Margin="0,2"/>
                        <TextBlock Text="• تحديد مواعيد العمل بوضوح مسبقاً." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>

                <!-- Term 5 -->
                <StackPanel Margin="20,0,0,15">
                    <TextBlock Text="البند الخامس: الأجرة والدفع" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                    <StackPanel Margin="10,0,0,0">
                        <StackPanel Orientation="Horizontal" Margin="0,2">
                            <TextBlock Text="• إجمالي أجرة النقل: " FontSize="11"/>
                            <TextBlock Text="{Binding SelectedOffer.TotalPrice}" FontWeight="Bold" FontSize="11"/>
                            <TextBlock Text=" ريال يمني" FontSize="11"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,2">
                            <TextBlock Text="• أجرة اليوم الواحد: " FontSize="11"/>
                            <TextBlock Text="{Binding SelectedOffer.DailyRate}" FontWeight="Bold" FontSize="11"/>
                            <TextBlock Text=" ريال يمني" FontSize="11"/>
                        </StackPanel>
                        <TextBlock Text="• يتم الدفع عند انتهاء المهمة وتسليم التقرير النهائي." FontSize="11" Margin="0,2"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>

            <!-- Footer -->
            <Grid Margin="0,40,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="الطرف الأول" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5"/>
                    <TextBlock Text="التوقيع: ________________" HorizontalAlignment="Center" Margin="0,20"/>
                    <TextBlock Text="التاريخ: ________________" HorizontalAlignment="Center" Margin="0,5"/>
                </StackPanel>

                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="الطرف الثاني" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SelectedDriver.Name}" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5"/>
                    <TextBlock Text="التوقيع: ________________" HorizontalAlignment="Center" Margin="0,20"/>
                    <TextBlock Text="التاريخ: ________________" HorizontalAlignment="Center" Margin="0,5"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </Border>
</UserControl>
